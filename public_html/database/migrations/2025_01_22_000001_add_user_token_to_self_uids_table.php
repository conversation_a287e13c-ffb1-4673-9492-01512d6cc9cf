<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserTokenToSelfUidsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('self_uids', function (Blueprint $table) {
            $table->string('user_token')->nullable()->after('self_uid')->comment('User token từ bản ghi tracking mới nhất');
            
            // Thêm index cho user_token
            $table->index('user_token');
            $table->index(['user_token', 'updated_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('self_uids', function (Blueprint $table) {
            $table->dropIndex(['user_token']);
            $table->dropIndex(['user_token', 'updated_at']);
            $table->dropColumn('user_token');
        });
    }
}
