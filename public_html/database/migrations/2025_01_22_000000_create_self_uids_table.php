<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSelfUidsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('self_uids', function (Blueprint $table) {
            $table->id();
            $table->string('self_uid')->unique()->comment('Self UID duy nhất');
            $table->bigInteger('diamond_balance')->default(0)->comment('Số diamond hiện tại');
            $table->bigInteger('bean_balance')->default(0)->comment('Số bean hiện tại');
            $table->string('ip_address')->nullable()->comment('IP address cuối cùng');
            $table->timestamps();

            // Indexes để tối ưu query
            $table->index(['diamond_balance', 'created_at']);
            $table->index(['bean_balance', 'created_at']);
            $table->index(['ip_address', 'created_at']);
            $table->index('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('self_uids');
    }
}
