<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserIdToSelfUidTrackingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('self_uid_trackings', function (Blueprint $table) {
            // Thêm trường user_id
            $table->unsignedBigInteger('user_id')->nullable()->after('user_token')->comment('ID của user từ bảng users');
            
            // Tạo foreign key constraint
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            
            // Tạo các index để tối ưu performance
            $table->index('user_id');
            $table->index(['user_id', 'created_at']);
            $table->index(['user_id', 'self_uid']);
            $table->index(['user_id', 'self_uid', 'created_at']);
            $table->index(['user_id', 'diamond_balance']);
            $table->index(['user_id', 'bean_balance']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('self_uid_trackings', function (Blueprint $table) {
            // Xóa foreign key trước
            $table->dropForeign(['user_id']);
            
            // Xóa các index
            $table->dropIndex(['user_id']);
            $table->dropIndex(['user_id', 'created_at']);
            $table->dropIndex(['user_id', 'self_uid']);
            $table->dropIndex(['user_id', 'self_uid', 'created_at']);
            $table->dropIndex(['user_id', 'diamond_balance']);
            $table->dropIndex(['user_id', 'bean_balance']);
            
            // Xóa column
            $table->dropColumn('user_id');
        });
    }
}
