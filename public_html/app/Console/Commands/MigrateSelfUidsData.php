<?php

namespace App\Console\Commands;

use App\Models\SelfUid;
use App\Models\SelfUidTracking;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateSelfUidsData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'selfuid:migrate-data {--force : Force migration without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate data from self_uid_trackings to self_uids table';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting migration from self_uid_trackings to self_uids...');

        // Kiểm tra xem bảng self_uids đã có dữ liệu chưa
        $existingCount = SelfUid::count();
        if ($existingCount > 0 && !$this->option('force')) {
            if (!$this->confirm("Bảng self_uids đã có {$existingCount} bản ghi. Bạn có muốn tiếp tục?")) {
                $this->info('Migration cancelled.');
                return 0;
            }
        }

        // L<PERSON>y danh sách tất cả self_uid duy nhất
        $this->info('Getting unique self_uids...');
        $uniqueSelfUids = SelfUidTracking::select('self_uid')
            ->distinct()
            ->pluck('self_uid');

        $this->info("Found {$uniqueSelfUids->count()} unique self_uids");

        $progressBar = $this->output->createProgressBar($uniqueSelfUids->count());
        $progressBar->start();

        $migratedCount = 0;
        $updatedCount = 0;

        foreach ($uniqueSelfUids as $selfUid) {
            // Lấy bản ghi mới nhất của self_uid này
            $latestTracking = SelfUidTracking::where('self_uid', $selfUid)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($latestTracking) {
                // Tạo hoặc cập nhật trong bảng self_uids
                $selfUidRecord = SelfUid::updateOrCreate(
                    ['self_uid' => $selfUid],
                    [
                        'user_token' => $latestTracking->user_token,
                        'diamond_balance' => $latestTracking->diamond_balance,
                        'bean_balance' => $latestTracking->bean_balance,
                        'ip_address' => $latestTracking->ip_address,
                    ]
                );

                if ($selfUidRecord->wasRecentlyCreated) {
                    $migratedCount++;
                } else {
                    $updatedCount++;
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Migration completed!");
        $this->info("- Created: {$migratedCount} records");
        $this->info("- Updated: {$updatedCount} records");
        $this->info("- Total: " . ($migratedCount + $updatedCount) . " records");

        // Hiển thị thống kê
        $this->showStats();

        return 0;
    }

    /**
     * Hiển thị thống kê sau khi migration
     */
    private function showStats()
    {
        $this->newLine();
        $this->info('=== STATISTICS ===');

        $totalRecords = SelfUid::count();
        $totalDiamond = SelfUid::sum('diamond_balance');
        $totalBean = SelfUid::sum('bean_balance');

        $this->info("Total Self UIDs: {$totalRecords}");
        $this->info("Total Diamond: " . number_format($totalDiamond));
        $this->info("Total Bean: " . number_format($totalBean));

        // Top 5 self_uid có diamond cao nhất
        $this->info("\nTop 5 Self UIDs by Diamond:");
        $topDiamond = SelfUid::orderBy('diamond_balance', 'desc')->limit(5)->get();
        foreach ($topDiamond as $index => $record) {
            $this->info(($index + 1) . ". {$record->self_uid}: " . number_format($record->diamond_balance) . " diamond");
        }

        // Top 5 self_uid có bean cao nhất
        $this->info("\nTop 5 Self UIDs by Bean:");
        $topBean = SelfUid::orderBy('bean_balance', 'desc')->limit(5)->get();
        foreach ($topBean as $index => $record) {
            $this->info(($index + 1) . ". {$record->self_uid}: " . number_format($record->bean_balance) . " bean");
        }
    }
}
