<?php

namespace App\Console\Commands;

use App\Models\SelfUid;
use App\Models\SelfUidTracking;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateUserIdData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'selfuid:migrate-user-id {--force : Force migration without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate user_token to user_id in self_uids and self_uid_trackings tables';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting migration from user_token to user_id...');

        if (!$this->option('force')) {
            if (!$this->confirm('Bạn có muốn tiếp tục migrate dữ liệu user_token sang user_id?')) {
                $this->info('Migration cancelled.');
                return 0;
            }
        }

        // Step 1: Migrate self_uid_trackings table
        $this->info('Step 1: Migrating self_uid_trackings table...');
        $this->migrateTrackingTable();

        // Step 2: Migrate self_uids table
        $this->info('Step 2: Migrating self_uids table...');
        $this->migrateSelfUidsTable();

        $this->info('Migration completed successfully!');
        return 0;
    }

    /**
     * Migrate self_uid_trackings table
     */
    private function migrateTrackingTable()
    {
        // Lấy tất cả bản ghi có user_token nhưng chưa có user_id
        $trackings = SelfUidTracking::whereNotNull('user_token')
            ->whereNull('user_id')
            ->get();

        $this->info("Found {$trackings->count()} tracking records to migrate");

        if ($trackings->isEmpty()) {
            $this->info('No tracking records need migration');
            return;
        }

        $progressBar = $this->output->createProgressBar($trackings->count());
        $progressBar->start();

        $updatedCount = 0;
        $errorCount = 0;

        foreach ($trackings as $tracking) {
            try {
                $user = User::where('user_token', $tracking->user_token)->first();
                if ($user) {
                    $tracking->update(['user_id' => $user->id]);
                    $updatedCount++;
                } else {
                    $this->warn("\nUser not found for token: {$tracking->user_token}");
                    $errorCount++;
                }
            } catch (\Exception $e) {
                $this->error("\nError updating tracking ID {$tracking->id}: " . $e->getMessage());
                $errorCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Tracking table migration completed: {$updatedCount} updated, {$errorCount} errors");
    }

    /**
     * Migrate self_uids table
     */
    private function migrateSelfUidsTable()
    {
        // Lấy tất cả bản ghi có user_token nhưng chưa có user_id
        $selfUids = SelfUid::whereNotNull('user_token')
            ->whereNull('user_id')
            ->get();

        $this->info("Found {$selfUids->count()} self_uid records to migrate");

        if ($selfUids->isEmpty()) {
            $this->info('No self_uid records need migration');
            return;
        }

        $progressBar = $this->output->createProgressBar($selfUids->count());
        $progressBar->start();

        $updatedCount = 0;
        $errorCount = 0;

        foreach ($selfUids as $selfUid) {
            try {
                $user = User::where('user_token', $selfUid->user_token)->first();
                if ($user) {
                    $selfUid->update(['user_id' => $user->id]);
                    $updatedCount++;
                } else {
                    $this->warn("\nUser not found for token: {$selfUid->user_token}");
                    $errorCount++;
                }
            } catch (\Exception $e) {
                $this->error("\nError updating self_uid ID {$selfUid->id}: " . $e->getMessage());
                $errorCount++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info("Self_uids table migration completed: {$updatedCount} updated, {$errorCount} errors");
    }
}
