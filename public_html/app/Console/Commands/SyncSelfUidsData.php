<?php

namespace App\Console\Commands;

use App\Services\SelfUidSyncService;
use Illuminate\Console\Command;

class SyncSelfUidsData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'selfuid:sync {--recent=60 : Sync only recently updated self_uids (minutes)} {--all : Sync all self_uids}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync self_uids data from tracking table';

    protected $syncService;

    public function __construct(SelfUidSyncService $syncService)
    {
        parent::__construct();
        $this->syncService = $syncService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting self_uids sync...');

        if ($this->option('all')) {
            // Đồng bộ tất cả
            $this->info('Syncing all self_uids...');
            $result = $this->syncService->syncAllSelfUids();
        } else {
            // Đồng bộ chỉ những cái đ<PERSON> cập nhật gần đây
            $minutes = $this->option('recent');
            $this->info("Syncing self_uids updated in the last {$minutes} minutes...");
            $result = $this->syncService->syncRecentlyUpdated($minutes);
        }

        if ($result['success']) {
            $this->info('Sync completed successfully!');
            $this->info("Total: {$result['total']}");
            $this->info("Synced: {$result['synced']}");
            if ($result['errors'] > 0) {
                $this->warn("Errors: {$result['errors']}");
            }
        } else {
            $this->error('Sync failed: ' . $result['error']);
            return 1;
        }

        return 0;
    }
}
