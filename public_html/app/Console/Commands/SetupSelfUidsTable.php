<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupSelfUidsTable extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'selfuid:setup {--force : Force setup without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup self_uids table and migrate initial data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== SETUP SELF UIDS TABLE ===');
        
        if (!$this->option('force')) {
            if (!$this->confirm('Bạn có muốn tiếp tục setup bảng self_uids và migrate dữ liệu?')) {
                $this->info('Setup cancelled.');
                return 0;
            }
        }

        // Step 1: Run migration
        $this->info('Step 1: Running migration...');
        try {
            Artisan::call('migrate', ['--force' => true]);
            $this->info('✓ Migration completed successfully');
        } catch (\Exception $e) {
            $this->error('✗ Migration failed: ' . $e->getMessage());
            return 1;
        }

        // Step 2: Migrate data
        $this->info('Step 2: Migrating data from self_uid_trackings...');
        try {
            Artisan::call('selfuid:migrate-data', ['--force' => true]);
            $this->info('✓ Data migration completed successfully');
        } catch (\Exception $e) {
            $this->error('✗ Data migration failed: ' . $e->getMessage());
            return 1;
        }

        $this->info('');
        $this->info('=== SETUP COMPLETED SUCCESSFULLY ===');
        $this->info('Bảng self_uids đã được tạo và dữ liệu đã được migrate.');
        $this->info('Từ giờ, giao diện "Danh sách SelfUID Tracking" sẽ hiển thị dữ liệu từ bảng mới.');
        $this->info('Dữ liệu sẽ được tự động đồng bộ khi có tracking mới.');

        return 0;
    }
}
