<?php

namespace App\Providers;

use App\Models\SelfUidTracking;
use App\Observers\SelfUidTrackingObserver;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        // Đăng ký observer để tự động đồng bộ dữ liệu
        SelfUidTracking::observe(SelfUidTrackingObserver::class);
    }
}
