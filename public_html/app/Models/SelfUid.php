<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SelfUid extends Model
{
    use HasFactory;

    protected $table = 'self_uids';

    protected $fillable = [
        'self_uid',
        'user_token',
        'user_id',
        'diamond_balance',
        'bean_balance',
        'ip_address'
    ];

    protected $casts = [
        'diamond_balance' => 'integer',
        'bean_balance' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Relationship với User
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * Scope để tìm theo self_uid
     */
    public function scopeBySelfUid($query, $selfUid)
    {
        return $query->where('self_uid', $selfUid);
    }

    /**
     * Scope để filter theo user_token (để tương thích với giao diện)
     */
    public function scopeByUserToken($query, $userToken)
    {
        return $query->whereHas('user', function($q) use ($userToken) {
            $q->where('user_token', $userToken);
        });
    }

    /**
     * Scope để filter theo user_id
     */
    public function scopeByUserId($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope để sắp xếp theo diamond
     */
    public function scopeOrderByDiamond($query, $direction = 'desc')
    {
        return $query->orderBy('diamond_balance', $direction);
    }

    /**
     * Scope để sắp xếp theo bean
     */
    public function scopeOrderByBean($query, $direction = 'desc')
    {
        return $query->orderBy('bean_balance', $direction);
    }

    /**
     * Scope để sắp xếp theo thời gian
     */
    public function scopeOrderByTime($query, $direction = 'desc')
    {
        return $query->orderBy('updated_at', $direction);
    }

    /**
     * Scope để sắp xếp theo IP
     */
    public function scopeOrderByIp($query, $direction = 'asc')
    {
        return $query->orderBy('ip_address', $direction);
    }

    /**
     * Lấy lịch sử tracking của self_uid này
     */
    public function trackingHistory()
    {
        return $this->hasMany(SelfUidTracking::class, 'self_uid', 'self_uid');
    }

    /**
     * Cập nhật thông tin từ bản ghi tracking mới nhất
     */
    public function updateFromLatestTracking()
    {
        $latestTracking = SelfUidTracking::where('self_uid', $this->self_uid)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($latestTracking) {
            $this->update([
                'diamond_balance' => $latestTracking->diamond_balance,
                'bean_balance' => $latestTracking->bean_balance,
                'ip_address' => $latestTracking->ip_address,
            ]);
        }

        return $this;
    }

    /**
     * Tạo hoặc cập nhật self_uid từ tracking data
     */
    public static function createOrUpdateFromTracking(SelfUidTracking $tracking)
    {
        return static::updateOrCreate(
            ['self_uid' => $tracking->self_uid],
            [
                'user_token' => $tracking->user_token,
                'user_id' => $tracking->user_id,
                'diamond_balance' => $tracking->diamond_balance,
                'bean_balance' => $tracking->bean_balance,
                'ip_address' => $tracking->ip_address,
            ]
        );
    }

    /**
     * Format diamond balance
     */
    public function getFormattedDiamondAttribute()
    {
        return number_format($this->diamond_balance);
    }

    /**
     * Format bean balance
     */
    public function getFormattedBeanAttribute()
    {
        return number_format($this->bean_balance);
    }
}
