<?php

namespace App\Services;

use App\Models\SelfUid;
use App\Models\SelfUidTracking;
use Illuminate\Support\Facades\Log;

class SelfUidSyncService
{
    /**
     * Đồng bộ một self_uid từ tracking data (dùng cho migration ban đầu)
     */
    public function syncSelfUid($selfUidValue)
    {
        try {
            // L<PERSON>y bản ghi tracking mới nhất của self_uid này
            $latestTracking = SelfUidTracking::where('self_uid', $selfUidValue)
                ->orderBy('created_at', 'desc')
                ->first();

            if (!$latestTracking) {
                Log::warning("No tracking data found for self_uid: {$selfUidValue}");
                return false;
            }

            // Tạo hoặc cập nhật trong bảng self_uids
            $selfUid = SelfUid::updateOrCreate(
                ['self_uid' => $selfUidValue],
                [
                    'diamond_balance' => $latestTracking->diamond_balance,
                    'bean_balance' => $latestTracking->bean_balance,
                    'ip_address' => $latestTracking->ip_address,
                ]
            );

            Log::info("Synced self_uid: {$selfUidValue}", [
                'diamond_balance' => $latestTracking->diamond_balance,
                'bean_balance' => $latestTracking->bean_balance,
                'ip_address' => $latestTracking->ip_address,
                'was_recently_created' => $selfUid->wasRecentlyCreated
            ]);

            return $selfUid;

        } catch (\Exception $e) {
            Log::error("Error syncing self_uid: {$selfUidValue}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Đồng bộ tất cả self_uids từ tracking data (dùng cho migration ban đầu)
     */
    public function syncAllSelfUids()
    {
        try {
            // Lấy danh sách tất cả self_uid duy nhất
            $uniqueSelfUids = SelfUidTracking::select('self_uid')
                ->distinct()
                ->pluck('self_uid');

            $syncedCount = 0;
            $errorCount = 0;

            foreach ($uniqueSelfUids as $selfUidValue) {
                $result = $this->syncSelfUid($selfUidValue);
                if ($result) {
                    $syncedCount++;
                } else {
                    $errorCount++;
                }
            }

            Log::info("Bulk sync completed", [
                'total_self_uids' => $uniqueSelfUids->count(),
                'synced_count' => $syncedCount,
                'error_count' => $errorCount
            ]);

            return [
                'success' => true,
                'total' => $uniqueSelfUids->count(),
                'synced' => $syncedCount,
                'errors' => $errorCount
            ];

        } catch (\Exception $e) {
            Log::error("Error in bulk sync", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
