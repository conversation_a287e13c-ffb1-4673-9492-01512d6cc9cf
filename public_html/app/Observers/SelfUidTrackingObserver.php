<?php

namespace App\Observers;

use App\Models\SelfUidTracking;
use App\Services\SelfUidSyncService;
use Illuminate\Support\Facades\Log;

class SelfUidTrackingObserver
{
    protected $syncService;

    public function __construct(SelfUidSyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Handle the SelfUidTracking "created" event.
     */
    public function created(SelfUidTracking $selfUidTracking)
    {
        try {
            // Đồng bộ self_uid khi có bản ghi tracking mới
            $this->syncService->syncSelfUid($selfUidTracking->self_uid);
            
            Log::info("Auto-synced self_uid after tracking created", [
                'self_uid' => $selfUidTracking->self_uid,
                'tracking_id' => $selfUidTracking->id
            ]);
        } catch (\Exception $e) {
            Log::error("Error auto-syncing self_uid after tracking created", [
                'self_uid' => $selfUidTracking->self_uid,
                'tracking_id' => $selfUidTracking->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle the SelfUidTracking "updated" event.
     */
    public function updated(SelfUidTracking $selfUidTracking)
    {
        try {
            // Đồng bộ self_uid khi có bản ghi tracking được cập nhật
            $this->syncService->syncSelfUid($selfUidTracking->self_uid);
            
            Log::info("Auto-synced self_uid after tracking updated", [
                'self_uid' => $selfUidTracking->self_uid,
                'tracking_id' => $selfUidTracking->id
            ]);
        } catch (\Exception $e) {
            Log::error("Error auto-syncing self_uid after tracking updated", [
                'self_uid' => $selfUidTracking->self_uid,
                'tracking_id' => $selfUidTracking->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
